<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-auto="false"
      :data="tableData"
      :init-param="initParam"
      :pagination="false"
      highlight-current-row
      @select-all="selectionChange"
      @select="handleSelect"
      :data-callback="dataCallback"
      row-key="path"
      table-key="matrixDown"
      style="flex: 1; min-height: 0"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-text class="mx-1">{{ t("matrix.downList.deviceDirectory") }}：</el-text>
          <el-select v-model="selectType" filterable allow-create @change="handleChange" style="width: 120px">
            <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-if="false" v-model="filePath" :placeholder="t('matrix.downList.addFile')" style="width: 200px" readonly> </el-input>
          <el-button type="primary" @click="addFileAndFolder" plain :icon="DocumentAdd" :title="t('device.fileDownload.addDownloadFilesAndFolders')">
          </el-button>
          <el-button type="success" :icon="Upload" @click="importFile">{{ t("matrix.common.import") }}</el-button>
          <el-button type="success" :icon="Download" @click="exportFile">{{ t("matrix.common.export") }}</el-button>
          <el-button type="danger" :disabled="scope.selectedList.length == 0" :icon="Delete" plain @click="batchDelete(scope.selectedList)">
            {{ t("matrix.common.delete") }}
          </el-button>
          <el-button type="danger" :icon="Delete" plain @click="batchClear">
            {{ t("matrix.common.clear") }}
          </el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="Upload" @click="handleMoveUp(scope.row)">{{ t("matrix.common.moveUp") }}</el-button>
        <el-button type="primary" link :icon="Download" @click="handleMoveDown(scope.row)">{{ t("matrix.common.moveDown") }}</el-button>
        <el-button type="primary" link :icon="Delete" @click="deleteSingleFile(scope.row)">{{ t("matrix.common.delete") }}</el-button>
      </template>
    </ProTable>
    <ProgressDialog ref="progressDialog"></ProgressDialog>
    <CustomFileSelector ref="customFileSelector" @confirm="handleCustomFileSelectorConfirm" />
  </div>
</template>

<script setup lang="tsx" name="downList">
import { ref, reactive, computed } from "vue";
import { FileItem } from "@/api/interface/biz/debug/fileitem";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Delete, Download, Upload, DocumentAdd } from "@element-plus/icons-vue";
import { MathUtils } from "@/utils/mathUtils";
import { createScopeDebugStore } from "@/stores/modules/debug";
import { osControlApi } from "@/api/modules/biz/os";
import ProgressDialog from "../../debug/device/dialog/ProgressDialog.vue";
import { matrixApi } from "@/api/modules/biz/matrix";
import { useMatrixStore } from "@/stores/modules/matrix";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";

const { t } = useI18n();
const globalStore = useGlobalStore();
const matrixStire = useMatrixStore();
const { downlist, selectDownIds } = storeToRefs(matrixStire);
const tableData = downlist;
const selectIds = selectDownIds;

const { addConsole } = createScopeDebugStore("matrix")();
const progressDialog = ref();
const proTable = ref<ProTableInstance>();
const customFileSelector = ref();

// 动态计算表格容器高度
const tableContainerHeight = computed(() => {
  // 获取页面可用高度
  const windowHeight = window.innerHeight;
  // 头部区域高度（如有固定头部可写死或动态获取）
  const headerHeight = 64; // 例如顶部导航栏高度
  // 控制台区域高度（如有固定高度可写死或动态获取）
  const consoleHeight = globalStore.consoleHeight || 200; // 200为默认控制台高度
  // 其它可能的间距
  const margin = 24; // 额外边距
  // 计算表格区域高度
  return `${windowHeight - headerHeight - consoleHeight - margin}px`;
});

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

// const tableData = ref<FileItem[]>([]);
const filePath = ref("");
const selectType = ref("/shr");
const selectOptions = [
  {
    value: "/shr",
    label: "/shr"
  }
];
const handleChange = value => {
  if (!selectOptions.some(item => item.value === value)) {
    selectOptions.push({
      value,
      label: value
    });
  }
};
const addFileAndFolder = () => {
  customFileSelector.value.open();
};

function addTableFile(paths) {
  if (Array.isArray(paths)) {
    paths.forEach(fileItem => {
      const result = tableData.value.filter(item => item.path === fileItem.path);
      if (result && result.length > 0) {
        ElMessage.error(t("matrix.downList.fileExists", { path: fileItem.path }));
        addConsole(t("matrix.downList.fileExists", { path: fileItem.path }));
      } else {
        tableData.value.push({
          checked: false,
          id: 0,
          fileName: fileItem.name,
          fileSize: fileItem.size,
          fileSizeAs: MathUtils.formatBytes(fileItem.size),
          type: "",
          path: fileItem.path,
          lastModified: formatDateTime(fileItem.modifiedAt),
          status: "",
          percentType: "",
          percent: 0,
          checkSum: 0,
          hasTask: false,
          taskid: "",
          fileParentPath: ""
        });
        filePath.value = fileItem.path;

        console.log(paths);
      }
    });
  } else {
    addTableItem(paths);
  }
}
function addTableItem(fileItem) {
  const result = tableData.value.filter(item => item.path === fileItem.path);
  if (result && result.length > 0) {
    ElMessage.error(t("matrix.downList.fileExists", { path: fileItem.path }));
    addConsole(t("matrix.downList.fileExists", { path: fileItem.path }));
  } else {
    tableData.value.push({
      checked: false,
      id: 0,
      fileName: fileItem.name,
      fileSize: fileItem.size,
      fileSizeAs: MathUtils.formatBytes(fileItem.size),
      type: "",
      path: fileItem.path,
      lastModified: formatDateTime(fileItem.modifiedAt),
      status: "",
      percentType: "",
      percent: 0,
      checkSum: 0,
      hasTask: false,
      taskid: "",
      fileParentPath: ""
    });
    filePath.value = fileItem.path;

    console.log(fileItem);
  }
}
const deleteSingleFile = (row: any) => {
  console.log("uploadSingFile", row);
  nextTick(() => {
    proTable.value?.element?.toggleRowSelection(row, false);
  });
  tableData.value = tableData.value.filter(item => row.path !== item.path);
  addConsole(t("matrix.downList.fileDeleted", { path: row.path }));
};

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};
// 上移操作
const handleMoveUp = (row: any) => {
  const index = findIndexByPath(row.path);
  if (index <= 0) return;
  // 交换当前元素和上一个元素
  const newData = [...tableData.value];
  [newData[index - 1], newData[index]] = [newData[index], newData[index - 1]];
  tableData.value = newData;
};
// 查找行索引
const findIndexByPath = (path: string) => {
  return tableData.value.findIndex(item => item.path === path);
};
// 下移操作
const handleMoveDown = (row: any) => {
  const index = findIndexByPath(row.path);
  if (index >= tableData.value.length - 1) return;
  // 交换当前元素和下一个元素
  const newData = [...tableData.value];
  [newData[index], newData[index + 1]] = [newData[index + 1], newData[index]];
  tableData.value = newData;
};
// 表格配置项
const columns = reactive<ColumnProps<FileItem>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("matrix.common.index"), fixed: "left", width: 70 },
  {
    prop: "fileName",
    label: t("matrix.downList.fileName"),
    sortable: true
  },
  {
    prop: "fileSize",
    label: t("matrix.downList.fileSize"),
    width: 120,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: scope => <span>{scope.row.fileSizeAs}</span>
  },
  { prop: "path", label: t("matrix.downList.filePath") },
  { prop: "lastModified", label: t("matrix.downList.lastModified") },
  { prop: "operation", label: t("matrix.common.operation"), fixed: "right", width: 280 }
]);

const formatDateTime = (timestamp): string => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

const batchDelete = async (rows: any[]) => {
  console.log(rows);
  const fileMap = rows.map(row => row.path);
  nextTick(() => {
    rows.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  });
  tableData.value = tableData.value.filter(item => !fileMap.includes(item.path));
  addConsole(t("matrix.downList.filesDeleted"));
};

const selectionChange = isSelectAll => {
  console.log("selectionChange", isSelectAll);
  const map = isSelectAll.map(item => item.path);
  tableData.value.forEach(row => {
    if (map.includes(row.path)) {
      selectIds.value = [...new Set([...selectIds.value, row.path])];
    } else {
      selectIds.value = selectIds.value.filter(path => path !== row.path);
    }
  });
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.path === row.path);
    if (isSelected) {
      selectIds.value = [...new Set([...selectIds.value, row.path])];
    } else {
      selectIds.value = selectIds.value.filter(name => name !== row.path);
    }
  });
};

// 导出文件列表
const exportFile = async () => {
  const defaultPath = t("matrix.downList.defaultExportFileName");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("matrix.downList.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  // 导出路径不存在返回
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  console.log("selectPath:", selectPath);

  progressDialog.value.show();

  try {
    const fileItems = toRaw(tableData.value);
    fileItems.forEach((item, index) => (item.index = index + 1));
    const result = await matrixApi.exportDownloadList({
      path,
      fileItems
    });
    // 判断返回结果中的code为0，提示成功，否则提示失败
    if (Number(result.code) === 0) {
      addConsole(t("matrix.downList.exportSuccess", { path }));
      ElMessageBox.alert(t("matrix.downList.exportSuccess", { path }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.downList.exportFailed"));
      ElMessageBox.alert(t("matrix.downList.exportFailed"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("matrix.downList.exportFailed"), error);
    ElMessageBox.alert(t("matrix.downList.exportFailed"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    progressDialog.value.hide();
  }
};
const importFile = async () => {
  console.log("importFile");
  const selectPath = await osControlApi.selectFileByParams({
    title: t("matrix.downList.importTitle"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });

  console.log("selectPath:", selectPath.path);

  // 导入路径不存在返回
  if (!selectPath.path) {
    return;
  }

  const path = String(selectPath.path);
  console.log("selectPath:", path);

  progressDialog.value.show();
  try {
    // 调用导入 API
    const response = await matrixApi.importDownloadList({ path });

    // 处理返回结果
    if (Number(response.code) === 0) {
      const resData = response.data;

      if (Array.isArray(resData)) {
        batchClear();
        resData.forEach(file => {
          const result = tableData.value.filter(item => item.path === file.path);
          if (result && result.length > 0) {
            ElMessage.error(t("matrix.downList.fileExists", { path: file.path }));
            addConsole(t("matrix.downList.fileExists", { path: file.path }));
          } else {
            const row: FileItem = {
              checked: false,
              id: file.id,
              fileName: file.fileName,
              fileSize: 0,
              fileSizeAs: file.fileSizeAs,
              type: "",
              path: file.path,
              lastModified: file.lastModified,
              status: "",
              percentType: "",
              percent: 0,
              checkSum: 0,
              hasTask: false,
              taskid: "",
              fileParentPath: ""
            };
            tableData.value.push(row);

            console.log(tableData.value);
          }
        });

        tableData.value.forEach(row => {
          selectIds.value = [...new Set([...selectIds.value, row.path])];
        });
        nextTick(() => {
          tableData.value.forEach(row => {
            proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.path));
          });
        });
        addConsole(t("matrix.downList.importSuccess"));
        ElMessageBox.alert(t("matrix.downList.importSuccess"), t("matrix.common.tips"), {
          confirmButtonText: t("matrix.common.confirm"),
          type: "success"
        });
      }
    } else {
      addConsole(t("matrix.downList.importFailed", { msg: response.msg }));
      ElMessageBox.alert(t("matrix.downList.importFailed", { msg: response.msg }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("matrix.downList.importFailed"), error);
    ElMessageBox.alert(t("matrix.downList.importFailed"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    progressDialog.value.hide();
  }
};

const batchClear = () => {
  console.log("batchClear");
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
  selectIds.value = [];
  tableData.value = [];
};

const handleCustomFileSelectorConfirm = (items: any) => {
  // 兼容单个和多个
  const convertedItems = Array.isArray(items) ? items : [items];
  const fileItems = convertedItems.map(item => ({
    path: item.path,
    name: item.name,
    size: item.size || 0,
    modifiedAt: item.modifiedAt || new Date(),
    isDirectory: item.isDirectory
  }));
  addTableFile(fileItems);
};

onMounted(() => {
  loadData();
  // addAllListeners();
});

const loadData = async () => {
  nextTick(() => {
    tableData.value.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.path));
    });
  });
};
// onMounted(() => {
//   loadData();
//   addAllListeners();
// });
// onBeforeUnmount(() => {
//   saveData();
//   removeAllListeners();
// });

// function removeAllListeners() {
//   window.removeEventListener("beforeunload", saveData);
// }

// function addAllListeners() {
//   window.addEventListener("beforeunload", saveData);
// }

// function saveData() {
//   localStorage.setItem("matirx.downloadSelectIds", JSON.stringify(selectIds));
//   localStorage.setItem("matirx.downloadPath", JSON.stringify(selectType.value));
//   localStorage.setItem("matirx.downloadData", JSON.stringify(tableData.value));
// }

// function loadData() {
//   setTimeout(() => {
//     const filePathValue = localStorage.getItem("matirx.downloadPath");
//     if (filePathValue) {
//       selectType.value = JSON.parse(filePathValue);
//     }
//     const tableValue = localStorage.getItem("matirx.downloadData");
//     if (tableValue) {
//       tableData.value = JSON.parse(tableValue);

//       const downSelectIds = localStorage.getItem("matirx.downloadSelectIds");
//       if (downSelectIds) {
//         selectIds = JSON.parse(downSelectIds);
//         console.log("get============", selectIds);
//         nextTick(() => {
//           tableData.value.forEach(row => {
//             proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.path));
//           });
//         });
//       }
//     }
//   }, 100);
// }
</script>

<style lang="scss" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: v-bind(tableContainerHeight);
  overflow: hidden;
}
.header {
  margin-bottom: 8px;
}
</style>
