<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-auto="false"
      table-key="fileDownload"
      :data="tableData"
      highlight-current-row
      :init-param="initParam"
      :pagination="false"
      :data-callback="dataCallback"
      row-key="path"
    >
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-text class="mx-1">{{ t("device.fileDownload.deviceDirectory") }}：</el-text>
          <el-select v-model="selectType" filterable allow-create @change="handleChange" style="width: 100px">
            <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-switch
            v-model="isReboot"
            :active-text="t('device.fileDownload.reboot')"
            :inactive-text="t('device.fileDownload.noReboot')"
            style="height: 10px"
          />
          <el-text v-if="false" class="mx-1">{{ t("device.fileDownload.selectFile") }}：</el-text>
          <el-input v-if="false" v-model="filePath" :placeholder="t('device.fileDownload.addDownloadFile')" style="width: 200px" readonly> </el-input>
          <el-button type="primary" @click="addFileAndFolder" plain :icon="FolderAdd" :title="t('device.fileDownload.addDownloadFilesAndFolders')" />
          <el-button type="info" :disabled="downloading" :icon="DocumentAdd" plain @click="showPackageDialog = true" :title="'程序打包'" />
          <el-button
            type="primary"
            :disabled="downloading || scope.selectedList.length == 0"
            :icon="Delete"
            @click="batchDownload(scope.selectedList)"
          >
            {{ t("device.fileDownload.downloadFile") }}
          </el-button>
          <el-button
            type="warning"
            :disabled="!downloading || scope.selectedList.length === 0"
            :icon="Delete"
            plain
            @click="batchCancel(scope.selectedList)"
          >
            {{ t("device.fileDownload.cancelDownload") }}
          </el-button>
          <el-button type="success" :disabled="downloading" :icon="Upload" @click="importFile">{{ t("device.fileDownload.importList") }}</el-button>
          <el-button type="success" :disabled="downloading" :icon="Download" @click="exportFile">{{ t("device.fileDownload.exportList") }}</el-button>
          <el-button
            type="danger"
            :disabled="downloading || scope.selectedList.length == 0"
            :icon="DeleteFilled"
            plain
            @click="batchDelete(scope.selectedList)"
            :title="t('device.fileDownload.batchDelete')"
          />
          <el-button type="danger" :disabled="downloading" :icon="Delete" plain @click="batchClear" :title="t('device.fileDownload.clearList')" />
        </div>
      </template>
      <template #expand="scope">
        {{ scope.row }}
      </template>
      <template #operation="scope">
        <el-button
          type="primary"
          link
          :icon="Download"
          v-if="!scope.row.hasTask"
          :disabled="scope.row.status === t('device.fileDownload.waitingDownload')"
          @click="downSingFile(scope.row)"
        >
          {{ t("device.fileDownload.download") }}
        </el-button>
        <el-button type="primary" v-if="scope.row.hasTask" :icon="Dish" link @click="cancelDownSingFile(scope.row)">{{
          t("device.fileDownload.cancelDownload")
        }}</el-button>
        <el-button
          type="primary"
          link
          v-if="!scope.row.hasTask"
          :disabled="scope.row.status === t('device.fileDownload.waitingDownload')"
          :icon="Delete"
          @click="deleteSingleFile(scope.row)"
        >
          {{ t("device.fileDownload.delete") }}
        </el-button>
      </template>
    </ProTable>
    <ProgressDialog ref="progressDialog"></ProgressDialog>
    <CustomFileSelector ref="customFileSelector" @confirm="handleCustomFileSelectorConfirm" />
    <FilePackageDialog v-model="showPackageDialog" width="1200px" @add-to-download="handleAddPackagedFile" />
    <el-dialog v-model="packageDialogVisible" width="900px" :title="t('device.fileDownload.packageProgram')" :close-on-click-modal="false">
      <div class="mb-4">
        <el-input v-model="packageSaveDir" :placeholder="t('device.fileDownload.selectSaveDir')" readonly style="width: 350px">
          <template #append>
            <el-button :icon="FolderAdd" @click="selectPackageSaveDir" />
          </template>
        </el-input>
      </div>
      <el-table :data="tableData" style="width: 100%; max-height: 400px; margin-bottom: 10px" height="400">
        <el-table-column prop="fileName" :label="t('device.fileDownload.fileName')" />
        <el-table-column prop="fileSizeAs" :label="t('device.fileDownload.fileSize')" />
        <el-table-column prop="path" :label="t('device.fileDownload.filePath')" />
      </el-table>
      <div class="flex justify-end mt-4">
        <el-button @click="packageDialogVisible = false">{{ t("device.fileDownload.cancel") }}</el-button>
        <el-button type="primary" :loading="packaging" @click="handlePackageProgram">{{ t("device.fileDownload.packageBtn") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { FileItem, UpadRpcFileDownloadItem } from "@/api/interface/biz/debug/fileitem";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Delete, Download, Upload, Dish, FolderAdd } from "@element-plus/icons-vue";
// 新增引入 DeleteFilled
import { DeleteFilled, DocumentAdd } from "@element-plus/icons-vue";
// import { UploadUserFile } from "element-plus";
// import { MathUtils } from "@/utils/mathUtils";
import { ipc } from "@/api/request/ipcRenderer";
import { devicefileApi } from "@/api/modules/biz/debug/devicefile";
import { IECNotify } from "@/api/interface";
import { useDebugStore } from "@/stores/modules/debug";
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
import { osControlApi } from "@/api/modules/biz/os";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { deviceoperationApi } from "@/api/modules/biz/debug/deviceoperation";
import { MathUtils } from "@/utils/mathUtils";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { nextTick } from "vue";
import { matrixApi } from "@/api/modules/biz/matrix/matrix";
import FilePackageDialog from "../dialog/FilePackageDialog.vue";
import { formatDateTime } from "@/utils";
const { currDevice } = useDebugStore();
const { addConsole } = useDebugStore();
const downloading = ref(false);
const isReboot = ref(false);
const progressDialog = ref();
const customFileSelector = ref();
// import { osControlApi } from "@/api/modules/biz/os/control";
// // import { variableApi } from "@/api/modules/biz/debug/variable";
// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const tableData = ref<FileItem[]>([]);
const filePath = ref("");
const showPackageDialog = ref(false);
// const fileList = ref<UploadUserFile[]>([]);
const selectType = ref("/shr");
const selectOptions = [
  {
    value: "/dwld",
    label: "/dwld"
  },
  {
    value: "/shr",
    label: "/shr"
  },
  {
    value: "/shr/configuration",
    label: "/shr/configuration"
  }
];
const { t } = useI18n();
const handleChange = value => {
  if (!selectOptions.some(item => item.value === value)) {
    selectOptions.push({
      value,
      label: value
    });
  }
};
const addFileAndFolder = async () => {
  customFileSelector.value.open();
};

const handleCustomFileSelectorConfirm = (items: any) => {
  // 将自定义文件选择器返回的数据转换为addTableFile期望的格式
  const convertedItems = Array.isArray(items) ? items : [items];
  const fileItems = convertedItems.map(item => ({
    path: item.path,
    name: item.name,
    size: item.size || 0,
    modifiedAt: item.modifiedAt || new Date(),
    isDirectory: item.isDirectory
  }));

  addTableFile(fileItems);
};

function addTableFile(paths) {
  if (Array.isArray(paths)) {
    paths.forEach(fileItem => {
      const result = tableData.value.filter(item => item.path === fileItem.path);
      if (result && result.length > 0) {
        ElMessage.error(t("device.fileDownload.fileExists", { path: fileItem.path }));
        addConsole(t("device.fileDownload.fileExists", { path: fileItem.path }));
      } else {
        // 处理文件大小显示
        let fileSize = fileItem.size || 0;
        let fileSizeAs = MathUtils.formatBytes(fileSize);
        tableData.value.push({
          checked: false,
          id: 0,
          fileName: fileItem.name,
          fileSize: fileSize,
          fileSizeAs: fileSizeAs,
          type: "",
          path: fileItem.path,
          lastModified: formatDateTime(fileItem.modifiedAt),
          status: "",
          percentType: "",
          percent: 0,
          checkSum: 0,
          hasTask: false,
          taskid: "",
          fileParentPath: "",
          rename: ""
        });
        filePath.value = fileItem.path;

        console.log(paths);
      }
    });
  } else {
    addTableItem(paths);
  }
}
function addTableItem(fileItem) {
  const result = tableData.value.filter(item => item.path === fileItem.path);
  if (result && result.length > 0) {
    ElMessage.error(t("device.fileDownload.fileExists", { path: fileItem.path }));
    addConsole(t("device.fileDownload.fileExists", { path: fileItem.path }));
  } else {
    // 处理文件大小显示
    let fileSize = fileItem.size || 0;
    let fileSizeAs = "";

    if (fileItem.isDirectory) {
      // 文件夹显示特殊标识
      fileSizeAs = t("device.fileDownload.folder");
    } else {
      // 文件显示实际大小
      fileSizeAs = MathUtils.formatBytes(fileSize);
    }

    tableData.value.push({
      checked: false,
      id: 0,
      fileName: fileItem.name,
      fileSize: fileSize,
      fileSizeAs: fileSizeAs,
      type: "",
      path: fileItem.path,
      lastModified: formatDateTime(fileItem.modifiedAt),
      status: "",
      percentType: "",
      percent: 0,
      checkSum: 0,
      hasTask: false,
      taskid: "",
      fileParentPath: "",
      rename: ""
    });
    filePath.value = fileItem.path;

    console.log(fileItem);
  }
}
const reboot = async () => {
  if (isReboot.value) {
    const result = await deviceoperationApi.rebootDeviceByDevice(props.deviceId)();
    if (Number(result.code) == 0) {
      addConsole(t("device.fileDownload.rebootSuccess"));
    } else {
      addConsole(t("device.fileDownload.rebootFailed", { msg: result.msg }));
    }
  }
};
const downSingFile = async (row: any) => {
  if (String(row.fileName).endsWith("\\")) {
    ElMessageBox.alert(t("device.fileDownload.selectValidFile"), t("device.fileDownload.errorTitle"), {
      type: "error"
    });
    return;
  }
  if (!selectType.value) {
    ElMessageBox.alert(t("device.fileDownload.remotePathEmpty"), t("device.fileDownload.errorTitle"), {
      type: "error"
    });
    return;
  }
  let filePath = row.path;
  if (row.rename && row.rename !== row.fileName) {
    // 复制一份临时文件，调用后端API
    const dotIndex = row.rename.lastIndexOf(".");
    const ext = dotIndex !== -1 ? row.rename.substring(dotIndex) : "";
    const base = dotIndex !== -1 ? row.rename.substring(0, dotIndex) : row.rename;
    const tempDir = window.navigator.userAgent.includes("Windows") ? "C:/Windows/Temp" : "/tmp";
    const tempPath = `${tempDir}/${base}${ext}`;
    try {
      await osControlApi.copyFile({ src: row.path, dest: tempPath });
      filePath = tempPath;
      row.tempPath = tempPath; // 存储临时路径
      console.log(filePath);
    } catch (e) {
      const err = e as Error;
      ElMessage.error(t("device.fileDownload.renameCopyFailed") + err.message);
      return;
    }
  }
  const result = await devicefileApi.downloadDeviceFileByDevice(props.deviceId, {
    remoteParentPath: selectType.value,
    fileItems: [
      {
        filePath
      }
    ]
  });
  if (Number(result.code) == 0) {
    addConsole(t("device.fileDownload.downloadComplete"));
    reboot();
  } else {
    addConsole(t("device.fileDownload.downloadError") + result.msg);
  }
};

const cancelDownSingFile = async (row: any) => {
  console.log("uploadSingFile", row);
  if (row.taskid == "") {
    ElMessageBox.alert(t("device.fileDownload.noDownloadTask"), t("device.fileDownload.errorTitle"), {
      type: "error"
    });
    return;
  }
  // 只传 taskids，不传 filePaths
  const result = await devicefileApi.cancelDownloadDeviceFileByDevice(props.deviceId, {
    taskids: [row.taskid]
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileDownload.downloadCancelled"));
    addConsole(t("device.fileDownload.downloadCancelled", { path: row.path }));
    const items = Array.isArray(result.data) ? result.data : [];
    const targetNames = new Set(items.map(item => item.filePath));
    proTable.value?.tableData.forEach(row => {
      // 判断临时路径或原始路径是否命中
      if (row.status === t("device.fileDownload.waitingDownload") && (targetNames.has(row.path) || (row.tempPath && targetNames.has(row.tempPath)))) {
        row.status = t("device.fileDownload.userCancelled");
        row.taskid = "";
      }
    });
  } else {
    addConsole(t("device.fileDownload.downloadCancelledFailed", { path: row.path, msg: result.msg }));
  }
};

const deleteSingleFile = (row: any) => {
  console.log("uploadSingFile", row);
  nextTick(() => {
    proTable.value?.element?.toggleRowSelection(row, false);
  });
  tableData.value = tableData.value.filter(item => row.path !== item.path);
  addConsole(t("device.fileDownload.fileDeleted", { path: row.path }));
};

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

// 表格配置项
const columns = reactive<ColumnProps<FileItem>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("device.fileDownload.sequence"), fixed: "left", width: 60 },
  {
    prop: "fileName",
    label: t("device.fileDownload.fileName"),
    sortable: true
  },
  {
    prop: "rename",
    label: t("device.fileDownload.rename"),
    width: 180,
    render: scope => {
      return (
        <el-input v-model={scope.row.rename} placeholder={t("device.fileDownload.renamePlaceholder")} onInput={val => updateRename(scope.row, val)} />
      );
    }
  },
  {
    prop: "fileSize",
    label: t("device.fileDownload.fileSize"),
    width: 120,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // a、b 是整行数据，这里按原始字节数比较
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: scope => <span>{scope.row.fileSizeAs}</span>
  },
  {
    prop: "path",
    label: t("device.fileDownload.filePath"),
    sortable: true
  },
  {
    prop: "lastModified",
    label: t("device.fileDownload.lastModified"),
    isShow: false,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按时间排序
      const timeA = new Date(a.lastModified).getTime() || 0;
      const timeB = new Date(b.lastModified).getTime() || 0;
      return timeA - timeB;
    }
  },
  {
    prop: "percent",
    label: t("device.fileDownload.progress"),
    render: scope => {
      return (
        <div>
          <el-progress percentage={scope.row.percent} text-inside={true} stroke-width={16} status={scope.row.percentType}></el-progress>
        </div>
      );
    }
  },
  {
    prop: "status",
    label: t("device.fileDownload.status"),
    width: 120
  },
  { prop: "operation", label: t("device.fileDownload.operation"), fixed: "right", width: 200 }
]);

// const handleFileChange = file => {
//   console.log(file);
//   const result = tableData.value.filter(item => item.path === file.raw.path);
//   if (result && result.length > 0) {
//     ElMessage.error("文件" + file.raw.path + "已经存在，添加失败！");
//     addConsole("文件" + file.raw.path + "已经存在，添加失败！");
//     return;
//   }

//   tableData.value.push({
//     checked: false,
//     id: 0,
//     fileName: file.name,
//     fileSize: file.size,
//     fileSizeAs: MathUtils.formatBytes(file.size),
//     type: file.raw.type,
//     path: file.raw.path,
//     lastModified: formatDateTime(file.raw.lastModified),
//     status: "",
//     percentType: "",
//     percent: 0,
//     checkSum: 0,
//     hasTask: false,
//     taskid: "",
//     fileParentPath: ""
//   });
//   filePath.value = file.raw.path;
// };
// const handleFolderChange = file => {
//   console.log(file);
//   const result = tableData.value.filter(item => item.path === file.raw.path);
//   if (result && result.length > 0) {
//     ElMessage.error("文件" + file.raw.path + "已经存在，添加失败！");
//     addConsole("文件" + file.raw.path + "已经存在，添加失败！");
//     return;
//   }

//   tableData.value.push({
//     checked: false,
//     id: 0,
//     fileName: file.name,
//     fileSize: file.size,
//     fileSizeAs: MathUtils.formatBytes(file.size),
//     type: file.raw.type,
//     path: file.raw.path,
//     lastModified: formatDateTime(file.raw.lastModified),
//     status: "",
//     percentType: "",
//     percent: 0,
//     checkSum: 0,
//     hasTask: false,
//     taskid: "",
//     fileParentPath: ""
//   });
//   filePath.value = file.raw.path;
// };

const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const notifyData = notify.data as any;
  const fileItem = notifyData.fileItem as UpadRpcFileDownloadItem;
  const status = notifyData.status;
  const progress = notifyData.progress.filePercentage;
  const taskid = notifyData.taskid;
  const errorMsg = notifyData.errorMsg;
  // 优先用 tempPath 匹配
  let row = proTable.value?.tableData.find(row => row.tempPath && row.tempPath === fileItem.filePath);
  if (!row) {
    // 再用 path 匹配
    row = proTable.value?.tableData.find(row => row.path === fileItem.filePath);
  }
  if (!row) {
    // 最后用文件名与 rename 匹配
    const getFileName = (path: string) => {
      if (!path) return "";
      const parts = path.split(/[/\\]/);
      return parts[parts.length - 1];
    };
    const fileName = getFileName(fileItem.filePath);
    row = proTable.value?.tableData.find(row => row.rename && row.rename === fileName);
  }
  if (!row) return;
  row.taskid = taskid;
  let msg = "";
  if (status === "CHECK_FILE_INFO") {
    msg = t("device.fileDownload.calculatingFileInfo");
    row.percentType = "";
    row.hasTask = true;
  } else if (status === "INIT") {
    msg = t("device.fileDownload.downloadPreparing");
    row.percentType = "";
    row.hasTask = true;
  } else if (status === "DATA_TRANSFER") {
    msg = t("device.fileDownload.downloading");
    row.percentType = "";
    row.hasTask = true;
  } else if (status === "SINGLE_FILE_FINISH") {
    msg = t("device.fileDownload.downloadComplete");
    row.percentType = "success";
    row.taskid = "";
    row.hasTask = false;
    // 下载完成后去掉复选框勾选状态
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  } else if (status === "ERROR") {
    msg = t("device.fileDownload.downloadError") + errorMsg;
    row.percentType = "exception";
    row.taskid = "";
    row.hasTask = false;
    // 下载失败后也自动取消勾选，方便下次只下载未完成的文件
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  } else if (status === "USER_CANCLE") {
    msg = t("device.fileDownload.userCancelled");
    row.percentType = "warning";
    row.taskid = "";
    row.hasTask = false;
    // 用户取消后也自动取消勾选
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  } else if (status === "ALL_FILE_FINISH") {
    msg = t("device.fileDownload.allFilesComplete");
    row.percentType = "success";
    row.taskid = "";
    row.hasTask = false;
    // 所有文件完成后自动取消勾选
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  }
  if (notify.type == "fileDownload") {
    row.percent = progress;
    row.status = msg;
    return;
  }
};

const dowloadloadingItem = computed(() => {
  return tableData.value.filter(item => item.hasTask);
});

watch(
  () => dowloadloadingItem.value.length,
  (newVal, oldVal) => {
    console.log(oldVal, "=>", newVal);
    if (newVal > 0) {
      downloading.value = true;
    } else {
      downloading.value = false;
    }
  }
);
const batchDelete = async (rows: any[]) => {
  const orderMap = new Map();
  // 获取表格当前显示的排序后数据
  const tableElement = proTable.value?.element;
  const sortedData = tableElement?.store?.states?.data?.value || proTable.value?.tableData || tableData.value;
  sortedData.map((item, index) => {
    orderMap.set(item.path, index);
  });
  const arrayRows = [...rows].sort((a, b) => {
    const aIndex = orderMap.get(a.path);
    const bIndex = orderMap.get(b.path);
    return aIndex - bIndex;
  });
  console.log(arrayRows);
  const fileMap = arrayRows.map(row => row.path);
  nextTick(() => {
    arrayRows.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  });
  tableData.value = tableData.value.filter(item => !fileMap.includes(item.path));
  addConsole(t("device.fileDownload.fileDeleted"));
};
// 批量删除用户信息
const batchCancel = async (rows: any[]) => {
  const orderMap = new Map();
  // 获取表格当前显示的排序后数据
  const tableElement = proTable.value?.element;
  const sortedData = tableElement?.store?.states?.data?.value || proTable.value?.tableData || tableData.value;
  sortedData.map((item, index) => {
    orderMap.set(item.path, index);
  });
  const arrayRows = [...rows].sort((a, b) => {
    const aIndex = orderMap.get(a.path);
    const bIndex = orderMap.get(b.path);
    return aIndex - bIndex;
  });
  const taskids = arrayRows.map(item => item.taskid);
  if (taskids.length == 0) {
    ElMessageBox.alert(t("device.fileDownload.noDownloadTask"), t("device.fileDownload.errorTitle"), {
      type: "error"
    });
    return;
  }
  // 只传 taskids，不传 filePaths
  const result = await devicefileApi.cancelDownloadDeviceFileByDevice(props.deviceId, {
    taskids
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileDownload.downloadCancelled"));
    addConsole(t("device.fileDownload.downloadCancelled"));
    const items = Array.isArray(result.data) ? result.data : [];
    const targetNames = new Set(items.map(item => item.filePath));
    proTable.value?.tableData.forEach(row => {
      // 判断临时路径或原始路径是否命中
      if (row.status === t("device.fileDownload.waitingDownload") && (targetNames.has(row.path) || (row.tempPath && targetNames.has(row.tempPath)))) {
        row.status = t("device.fileDownload.userCancelled");
        row.taskid = "";
      }
    });
  } else {
    addConsole(t("device.fileDownload.downloadCancelledFailed", { msg: result.msg }));
  }
};
const batchDownload = async (rows: any[]) => {
  const orderMap = new Map();
  // 获取表格当前显示的排序后数据
  const tableElement = proTable.value?.element;
  const sortedData = tableElement?.store?.states?.data?.value || proTable.value?.tableData || tableData.value;
  sortedData.map((item, index) => {
    orderMap.set(item.path, index);
  });
  const arrayRows = [...rows].sort((a, b) => {
    const aIndex = orderMap.get(a.path);
    const bIndex = orderMap.get(b.path);
    return aIndex - bIndex;
  });
  console.log(arrayRows);
  const dirRows = arrayRows.filter(item => String(item.fileName).endsWith("\\"));
  if (dirRows.length > 0) {
    ElMessageBox.alert(t("device.fileDownload.selectValidFile"), t("device.fileDownload.errorTitle"), {
      type: "error"
    });
    return;
  }
  if (selectType.value == "" || selectType.value == null) {
    ElMessageBox.alert(t("device.fileDownload.remotePathEmpty"), t("device.fileDownload.errorTitle"), {
      type: "error"
    });
    return;
  }
  const fileItems: UpadRpcFileDownloadItem[] = [];
  for (const row of arrayRows) {
    let filePath = row.path;
    if (row.rename && row.rename !== row.fileName) {
      const dotIndex = row.rename.lastIndexOf(".");
      const ext = dotIndex !== -1 ? row.rename.substring(dotIndex) : "";
      const base = dotIndex !== -1 ? row.rename.substring(0, dotIndex) : row.rename;
      const tempDir = window.navigator.userAgent.includes("Windows") ? "C:/Windows/Temp" : "/tmp";
      const tempPath = `${tempDir}/${base}${ext}`;
      try {
        await osControlApi.copyFile({ src: row.path, dest: tempPath });
        console.log("tempPath:", tempPath);
        filePath = tempPath;
        row.tempPath = tempPath; // 存储临时路径
        console.log(filePath);
      } catch (e) {
        const err = e as Error;
        ElMessage.error(t("device.fileDownload.renameCopyFailed") + err.message);
        return;
      }
    }
    fileItems.push({
      filePath
    });
    row.percent = 0;
    row.status = t("device.fileDownload.waitingDownload");
  }
  const result = await devicefileApi.downloadDeviceFileByDevice(props.deviceId, {
    remoteParentPath: selectType.value,
    fileItems: fileItems
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileDownload.downloadComplete"));
    addConsole(t("device.fileDownload.downloadComplete"));
    reboot();
  } else {
    addConsole(t("device.fileDownload.downloadError") + result.msg);
    rows.forEach(row => {
      if (row.status === t("device.fileDownload.waitingDownload")) {
        row.status = t("device.fileDownload.downloadError");
        row.taskid = "";
      }
    });
  }
};

// 导出文件列表
const exportFile = async () => {
  const defaultPath = t("device.fileDownload.downloadList") + ".xlsx";
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.fileDownload.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  console.log("selectPath:", selectPath);

  progressDialog.value.show();

  try {
    const fileItems = toRaw(tableData.value);
    fileItems.forEach((item, index) => (item.id = index + 1));
    const result = await devicefileApi.exportDownloadDeviceFile(props.deviceId, {
      path,
      data: fileItems
    });
    if (Number(result.code) === 0) {
      addConsole(t("device.fileDownload.exportSuccess"));
      ElMessageBox.alert(t("device.fileDownload.exportSuccess"), t("device.fileDownload.tip"), {
        confirmButtonText: t("device.fileDownload.confirmButton"),
        type: "success"
      });
    } else {
      addConsole(t("device.fileDownload.exportFailed"));
      ElMessageBox.alert(t("device.fileDownload.exportFailed"), t("device.fileDownload.tip"), {
        confirmButtonText: t("device.fileDownload.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.fileDownload.exportFailed") + ":", error);
    ElMessageBox.alert(t("device.fileDownload.exportFailed"), t("device.fileDownload.tip"), {
      confirmButtonText: t("device.fileDownload.confirmButton"),
      type: "error"
    });
  } finally {
    progressDialog.value.hide();
  }
};

const importFile = async () => {
  console.log("importFile");
  const selectPath = await osControlApi.selectFileByParams({
    title: t("device.fileDownload.importTitle"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });

  console.log("selectPath:", selectPath.path);

  if (!selectPath.path) {
    return;
  }

  const path = String(selectPath.path);
  console.log("selectPath:", path);

  progressDialog.value.show();
  try {
    const response = await devicefileApi.importDownloadDeviceFile(props.deviceId, { path });

    if (Number(response.code) === 0) {
      const resData = response.data;

      if (Array.isArray(resData)) {
        resData.forEach(file => {
          const result = tableData.value.filter(item => item.path === file.path);
          if (result && result.length > 0) {
            ElMessage.error(t("device.fileDownload.fileExists", { path: file.path }));
            addConsole(t("device.fileDownload.fileExists", { path: file.path }));
          } else {
            const row = {
              checked: false,
              id: file.id,
              fileName: file.fileName,
              fileSize: 0,
              fileSizeAs: file.fileSizeAs,
              type: "",
              path: file.path,
              lastModified: file.lastModified,
              status: "",
              percentType: "",
              percent: 0,
              checkSum: 0,
              hasTask: false,
              taskid: "",
              fileParentPath: "",
              rename: ""
            };
            tableData.value.push(row);
          }
        });
      }
      addConsole(t("device.fileDownload.importSuccess"));
      ElMessageBox.alert(t("device.fileDownload.importSuccess"), t("device.fileDownload.tip"), {
        confirmButtonText: t("device.fileDownload.confirmButton"),
        type: "success"
      });
    } else {
      addConsole(t("device.fileDownload.importFailed", { msg: response.msg }));
      ElMessageBox.alert(t("device.fileDownload.importFailed", { msg: response.msg }), t("device.fileDownload.tip"), {
        confirmButtonText: t("device.fileDownload.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.fileDownload.importFailed") + ":", error);
    ElMessageBox.alert(t("device.fileDownload.importFailed"), t("device.fileDownload.tip"), {
      confirmButtonText: t("device.fileDownload.confirmButton"),
      type: "error"
    });
  } finally {
    progressDialog.value.hide();
  }
};

const batchClear = () => {
  console.log("batchClear");
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
  tableData.value = [];
};

onBeforeUnmount(() => {
  console.log(currDevice.id);
  localStorage.setItem(currDevice.id + ".downloadPath", JSON.stringify(selectType.value));
  localStorage.setItem(currDevice.id + ".downloadData", JSON.stringify(tableData.value));
  ipc.removeAllListeners("filedownload_notify");
});

onMounted(() => {
  loadData();
  addAllListeners();
});
onBeforeUnmount(() => {
  saveData();
  removeAllListeners();
});
function removeAllListeners() {
  window.removeEventListener("beforeunload", saveData);
  ipc.removeAllListeners("filedownload_notify");
}

function addAllListeners() {
  window.addEventListener("beforeunload", saveData);
  ipc.on("filedownload_notify", notifyMethod);
}

function saveData() {
  localStorage.setItem(currDevice.id + ".downloadPath", JSON.stringify(selectType.value));
  localStorage.setItem(currDevice.id + ".downloadData", JSON.stringify(tableData.value));
}

const loadData = async () => {
  setTimeout(() => {
    const filePathValue = localStorage.getItem(currDevice.id + ".downloadPath");
    if (filePathValue) {
      selectType.value = JSON.parse(filePathValue);
    }
    const tableValue = localStorage.getItem(currDevice.id + ".downloadData");
    if (tableValue) {
      tableData.value = JSON.parse(tableValue);
    }
  }, 100);
};

const updateRename = (row, value) => {
  row.rename = value;
};

const packageDialogVisible = ref(false);
const packageSaveDir = ref("");
const packaging = ref(false);
const packagedFilePath = ref("");
async function selectPackageSaveDir() {
  const res = await osControlApi.selectFolder();
  let dir = "";
  if (Array.isArray(res) && res.length > 0) {
    dir = res[0];
  } else if (typeof res === "string") {
    dir = res;
  } else if (res && typeof res.path === "string") {
    dir = res.path;
  }
  packageSaveDir.value = dir;
}
async function handlePackageProgram() {
  if (!packageSaveDir.value) {
    ElMessage.warning(t("device.fileDownload.selectSaveDir"));
    return;
  }
  const fileList = tableData.value.map(item => ({ ...item }));
  if (!fileList.length) {
    ElMessage.warning(t("device.fileDownload.noFileSelected"));
    return;
  }
  packaging.value = true;
  progressDialog.value.show();
  try {
    const result = await matrixApi.handlePackage({ saveDir: packageSaveDir.value, fileList });
    let zipPath = result?.data || result;
    packagedFilePath.value = zipPath;
    if ((result && result.code === 0) || zipPath) {
      ElMessageBox({
        message: t("device.fileDownload.packageSuccess") + (zipPath ? `\n${t("device.fileDownload.zipPath", { zipPath })}` : ""),
        title: t("device.fileDownload.tip"),
        type: "success",
        showCancelButton: true,
        confirmButtonText: t("device.fileDownload.confirmButton"),
        cancelButtonText: "添加到下载界面",
        callback: async action => {
          if (action === "cancel" && zipPath) {
            await handleAddPackagedFile(zipPath);
            packageDialogVisible.value = false;
          }
        }
      });
    } else {
      ElMessageBox.alert(t("device.fileDownload.packageFailed", { msg: result?.msg }), t("device.fileDownload.tip"), {
        confirmButtonText: t("device.fileDownload.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    const errorMsg = error && typeof error === "object" && "message" in error ? error.message : String(error);
    ElMessageBox.alert(t("device.fileDownload.packageFailed", { msg: errorMsg }), t("device.fileDownload.tip"), {
      confirmButtonText: t("device.fileDownload.confirmButton"),
      type: "error"
    });
  } finally {
    packaging.value = false;
    progressDialog.value.hide();
  }
}
// 原 handleAddPackagedFile 替换为异步实现
async function handleAddPackagedFile(zipPath) {
  if (!zipPath) return;
  const fileName = zipPath.split(/[\\/]/).pop();
  let fileSize = 0;
  try {
    const res = await osControlApi.getFileOrFolderSize({ path: zipPath });
    fileSize = res.size || 0;
  } catch (e) {
    fileSize = 0;
  }
  const fileSizeAs = MathUtils.formatBytes(fileSize);
  const stat = {
    checked: false,
    id: 0,
    fileName,
    fileSize,
    fileSizeAs,
    type: "",
    path: zipPath,
    lastModified: formatDateTime(Date.now()),
    status: "",
    percentType: "",
    percent: 0,
    checkSum: 0,
    hasTask: false,
    taskid: "",
    fileParentPath: "",
    rename: ""
  };
  if (!tableData.value.some(item => item.path === zipPath)) {
    tableData.value.push(stat);
    addConsole(t("device.fileDownload.fileAdded", { path: zipPath }));
  } else {
    ElMessage.warning(t("device.fileDownload.fileExists", { path: zipPath }));
  }
}
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}

.header {
  margin-bottom: 8px;
}
</style>
